# Blackjack Game - Godot版本

这是一个完全还原您blackjack-online项目设计的21点游戏。

## 🎮 游戏功能

### 基本功能
- ✅ 完整的21点规则
- ✅ 多面额筹码投注 (1, 5, 10, 25, 50, 100)
- ✅ 标准游戏操作 (Hit, Stand, Double, Surrender)
- ✅ 智能庄家逻辑
- ✅ 实时余额管理
- ✅ 牌数统计显示
- ✅ 消息提示系统

### 操作说明
1. **下注**: 点击筹码按钮下注
2. **开始游戏**: 点击"Deal"按钮开始新游戏
3. **游戏操作**:
   - **Hit**: 要一张牌
   - **Stand**: 停牌，庄家开始行动
   - **Double**: 双倍下注并只能再要一张牌
   - **Surrender**: 投降，返还一半赌注
4. **清除投注**: 点击"Clear"清除当前投注
5. **查看结果**: 游戏会自动计算胜负并更新余额

## 🎨 添加筹码图片

要使用自定义筹码图片，请将图片文件放在 `images/` 文件夹中，命名格式为：

```
images/chip_1.png    - $1筹码
images/chip_5.png    - $5筹码  
images/chip_10.png   - $10筹码
images/chip_25.png   - $25筹码
images/chip_50.png   - $50筹码
images/chip_100.png  - $100筹码
```

如果没有提供图片，游戏会自动使用彩色按钮和文字显示。

## 🎯 游戏规则

### 基本规则
- 目标：让手牌总点数尽可能接近21点，但不能超过
- A可以算作1点或11点
- J、Q、K都算作10点
- 其他牌按面值计算

### 特殊情况
- **Blackjack**: 前两张牌总和为21点，获得1.5倍奖金
- **Bust**: 超过21点，自动输掉
- **Push**: 与庄家点数相同，平局退还赌注

### 庄家规则
- 庄家在17点以下必须要牌
- 庄家在17点及以上必须停牌

## 🚀 运行游戏

1. 在Godot编辑器中打开项目
2. 运行 `game.tscn` 场景
3. 开始游戏！

## 🎨 界面设计

游戏界面完全还原了您的blackjack-online项目：
- 深绿色赌场背景
- 专业的卡牌显示
- 金色文字和边框
- 投注圆圈显示
- 实时消息提示

## 🔧 技术特性

- 使用Godot 4.x引擎开发
- 完全程序化UI创建
- 支持图片资源加载
- 响应式布局设计
- 完整的游戏状态管理

祝您游戏愉快！🎲
