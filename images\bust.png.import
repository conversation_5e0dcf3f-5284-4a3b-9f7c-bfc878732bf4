[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bqsy7ghk521vq"
path="res://.godot/imported/bust.png-35f8db4b782545ff05f7eb52a75ec3ae.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://images/bust.png"
dest_files=["res://.godot/imported/bust.png-35f8db4b782545ff05f7eb52a75ec3ae.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
