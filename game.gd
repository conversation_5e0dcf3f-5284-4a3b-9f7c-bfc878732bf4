extends Node2D

var deck = []
var player_hand = []
var dealer_hand = []
var game_state = "waiting"
var player_score = 0
var dealer_score = 0

var player_card_nodes = []
var dealer_card_nodes = []
var ui_elements = {}

var card_back_texture
var card_textures = {}
var table_texture

const CARD_SIZE = Vector2(120, 168)
const PLAYER_CARD_START = Vector2(400, 450)
const DEALER_CARD_START = Vector2(400, 200)
const CARD_SPACING = 140

func _ready():
	create_card_textures()
	setup_background()
	setup_ui()
	create_deck()
	shuffle_deck()
	start_new_game()

func create_card_textures():
	card_back_texture = create_card_back()

	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]

	for suit in suits:
		for rank in ranks:
			var key = rank + "_" + suit
			card_textures[key] = create_card_texture(rank, suit)

func create_card_back():
	var image = Image.create(int(CARD_SIZE.x), int(CARD_SIZE.y), false, Image.FORMAT_RGB8)
	image.fill(Color(0.2, 0.2, 0.8))

	for y in range(10, int(CARD_SIZE.y) - 10, 20):
		for x in range(10, int(CARD_SIZE.x) - 10, 20):
			image.set_pixel(x, y, Color.WHITE)
			image.set_pixel(x + 10, y + 10, Color.WHITE)

	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

func create_card_texture(rank, suit):
	var image = Image.create(int(CARD_SIZE.x), int(CARD_SIZE.y), false, Image.FORMAT_RGB8)
	image.fill(Color.WHITE)

	for i in range(5):
		for x in range(int(CARD_SIZE.x)):
			image.set_pixel(x, i, Color.BLACK)
			image.set_pixel(x, int(CARD_SIZE.y) - 1 - i, Color.BLACK)
		for y in range(int(CARD_SIZE.y)):
			image.set_pixel(i, y, Color.BLACK)
			image.set_pixel(int(CARD_SIZE.x) - 1 - i, y, Color.BLACK)

	var color = Color.RED if suit in ["Hearts", "Diamonds"] else Color.BLACK

	for y in range(20, 40):
		for x in range(10, 50):
			image.set_pixel(x, y, color)

	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

func setup_background():
	var screen_size = get_viewport().get_visible_rect().size

	var background = ColorRect.new()
	background.size = screen_size
	background.color = Color(0.0, 0.4, 0.0)
	add_child(background)

	var table = ColorRect.new()
	table.size = Vector2(screen_size.x - 100, 400)
	table.position = Vector2(50, screen_size.y / 2 - 200)
	table.color = Color(0.0, 0.6, 0.0)
	add_child(table)

	var table_border = ColorRect.new()
	table_border.size = table.size + Vector2(10, 10)
	table_border.position = table.position - Vector2(5, 5)
	table_border.color = Color(0.6, 0.3, 0.0)
	add_child(table_border)
	move_child(table_border, 1)

func setup_ui():
	var screen_size = get_viewport().get_visible_rect().size

	var title = Label.new()
	title.text = "🎰 CASINO BLACKJACK 🎰"
	title.position = Vector2(screen_size.x / 2 - 200, 20)
	title.add_theme_font_size_override("font_size", 36)
	title.add_theme_color_override("font_color", Color.GOLD)
	add_child(title)

	var dealer_label = Label.new()
	dealer_label.text = "DEALER"
	dealer_label.position = Vector2(50, 120)
	dealer_label.add_theme_font_size_override("font_size", 24)
	dealer_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(dealer_label)
	ui_elements["dealer_label"] = dealer_label

	var dealer_score_label = Label.new()
	dealer_score_label.text = "Score: ?"
	dealer_score_label.position = Vector2(50, 150)
	dealer_score_label.add_theme_font_size_override("font_size", 20)
	dealer_score_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(dealer_score_label)
	ui_elements["dealer_score"] = dealer_score_label

	var player_label = Label.new()
	player_label.text = "PLAYER"
	player_label.position = Vector2(50, 400)
	player_label.add_theme_font_size_override("font_size", 24)
	player_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(player_label)
	ui_elements["player_label"] = player_label

	var player_score_label = Label.new()
	player_score_label.text = "Score: 0"
	player_score_label.position = Vector2(50, 430)
	player_score_label.add_theme_font_size_override("font_size", 20)
	player_score_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(player_score_label)
	ui_elements["player_score"] = player_score_label

	var hit_button = Button.new()
	hit_button.text = "🃏 HIT"
	hit_button.position = Vector2(screen_size.x / 2 - 200, screen_size.y - 100)
	hit_button.size = Vector2(120, 50)
	hit_button.add_theme_font_size_override("font_size", 18)
	hit_button.pressed.connect(_on_hit_pressed)
	add_child(hit_button)
	ui_elements["hit_button"] = hit_button

	var stand_button = Button.new()
	stand_button.text = "✋ STAND"
	stand_button.position = Vector2(screen_size.x / 2 - 60, screen_size.y - 100)
	stand_button.size = Vector2(120, 50)
	stand_button.add_theme_font_size_override("font_size", 18)
	stand_button.pressed.connect(_on_stand_pressed)
	add_child(stand_button)
	ui_elements["stand_button"] = stand_button

	var new_game_button = Button.new()
	new_game_button.text = "🔄 NEW GAME"
	new_game_button.position = Vector2(screen_size.x / 2 + 80, screen_size.y - 100)
	new_game_button.size = Vector2(140, 50)
	new_game_button.add_theme_font_size_override("font_size", 18)
	new_game_button.pressed.connect(_on_new_game_pressed)
	add_child(new_game_button)
	ui_elements["new_game_button"] = new_game_button

	var message_label = Label.new()
	message_label.text = "🎲 Welcome to Casino Blackjack! 🎲"
	message_label.position = Vector2(screen_size.x / 2 - 200, screen_size.y - 150)
	message_label.add_theme_font_size_override("font_size", 22)
	message_label.add_theme_color_override("font_color", Color.GOLD)
	add_child(message_label)
	ui_elements["message"] = message_label

func create_deck():
	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]

	deck.clear()

	for suit in suits:
		for rank in ranks:
			var card = {
				"suit": suit,
				"rank": rank
			}
			deck.append(card)

func shuffle_deck():
	deck.shuffle()

func create_card_node(card, is_face_down = false):
	var card_sprite = Sprite2D.new()
	card_sprite.scale = Vector2(0.8, 0.8)

	if is_face_down:
		card_sprite.texture = card_back_texture
	else:
		var key = card["rank"] + "_" + card["suit"]
		card_sprite.texture = card_textures[key]

	add_child(card_sprite)
	return card_sprite

func animate_card_deal(card_node, target_position):
	var start_position = Vector2(50, 50)
	card_node.position = start_position
	card_node.scale = Vector2(0.1, 0.1)

	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(card_node, "position", target_position, 0.5)
	tween.tween_property(card_node, "scale", Vector2(0.8, 0.8), 0.5)
	tween.tween_property(card_node, "rotation", 0.0, 0.5)

	await tween.finished

func clear_card_nodes():
	for card_node in player_card_nodes:
		if card_node:
			card_node.queue_free()
	for card_node in dealer_card_nodes:
		if card_node:
			card_node.queue_free()

	player_card_nodes.clear()
	dealer_card_nodes.clear()

func start_new_game():
	clear_card_nodes()
	player_hand.clear()
	dealer_hand.clear()
	game_state = "playing"

	if deck.size() < 10:
		create_deck()
		shuffle_deck()

	await deal_card_animated(player_hand, false)
	await deal_card_animated(dealer_hand, true)
	await deal_card_animated(player_hand, false)
	await deal_card_animated(dealer_hand, false)

	update_ui()
	check_blackjack()

func deal_card(hand):
	if deck.size() > 0:
		var card = deck.pop_back()
		hand.append(card)
		return card
	return null

func deal_card_animated(hand, is_dealer_first_card = false):
	if deck.size() > 0:
		var card = deck.pop_back()
		hand.append(card)

		var is_face_down = is_dealer_first_card and hand == dealer_hand and dealer_hand.size() == 1
		var card_node = create_card_node(card, is_face_down)

		var target_position
		if hand == player_hand:
			target_position = PLAYER_CARD_START + Vector2(CARD_SPACING * (player_hand.size() - 1), 0)
			player_card_nodes.append(card_node)
		else:
			target_position = DEALER_CARD_START + Vector2(CARD_SPACING * (dealer_hand.size() - 1), 0)
			dealer_card_nodes.append(card_node)

		await animate_card_deal(card_node, target_position)
		return card
	return null

func flip_dealer_first_card():
	if dealer_card_nodes.size() > 0 and dealer_hand.size() > 0:
		var first_card_node = dealer_card_nodes[0]
		var first_card = dealer_hand[0]

		var tween = create_tween()
		tween.tween_property(first_card_node, "scale:x", 0.0, 0.2)
		await tween.finished

		var key = first_card["rank"] + "_" + first_card["suit"]
		first_card_node.texture = card_textures[key]

		tween = create_tween()
		tween.tween_property(first_card_node, "scale:x", 0.8, 0.2)

func get_card_value(card):
	var rank = card["rank"]
	if rank in ["J", "Q", "K"]:
		return 10
	elif rank == "A":
		return 11
	else:
		return int(rank)

func calculate_hand_value(hand):
	var total = 0
	var aces = 0

	for card in hand:
		var value = get_card_value(card)
		if card["rank"] == "A":
			aces += 1
		total += value

	while total > 21 and aces > 0:
		total -= 10
		aces -= 1

	return total

func update_ui():
	player_score = calculate_hand_value(player_hand)

	ui_elements["player_score"].text = "Score: " + str(player_score)

	if game_state == "playing":
		ui_elements["dealer_score"].text = "Score: ?"
	else:
		dealer_score = calculate_hand_value(dealer_hand)
		ui_elements["dealer_score"].text = "Score: " + str(dealer_score)

	ui_elements["hit_button"].disabled = (game_state != "playing")
	ui_elements["stand_button"].disabled = (game_state != "playing")

	if player_score > 21:
		ui_elements["player_score"].add_theme_color_override("font_color", Color.RED)
	elif player_score == 21:
		ui_elements["player_score"].add_theme_color_override("font_color", Color.GOLD)
	else:
		ui_elements["player_score"].add_theme_color_override("font_color", Color.YELLOW)

func check_blackjack():
	if player_score == 21 and player_hand.size() == 2:
		if dealer_score == 21 and dealer_hand.size() == 2:
			end_game("Push! Both have Blackjack!")
		else:
			end_game("Blackjack! You win!")
	elif dealer_score == 21 and dealer_hand.size() == 2:
		end_game("Dealer has Blackjack! You lose!")

func dealer_turn():
	game_state = "dealer_turn"
	await flip_dealer_first_card()
	update_ui()

	while calculate_hand_value(dealer_hand) < 17:
		await get_tree().create_timer(1.0).timeout
		await deal_card_animated(dealer_hand, false)
		update_ui()

	var final_dealer_score = calculate_hand_value(dealer_hand)
	var final_player_score = calculate_hand_value(player_hand)

	if final_dealer_score > 21:
		end_game("💥 Dealer busts! You win! 🎉")
	elif final_dealer_score > final_player_score:
		end_game("😞 Dealer wins! Better luck next time!")
	elif final_player_score > final_dealer_score:
		end_game("🎉 You win! Congratulations! 🎉")
	else:
		end_game("🤝 Push! It's a tie!")

func end_game(message):
	game_state = "game_over"
	ui_elements["message"].text = message
	update_ui()

	var tween = create_tween()
	tween.set_loops(3)
	tween.tween_property(ui_elements["message"], "modulate", Color.YELLOW, 0.3)
	tween.tween_property(ui_elements["message"], "modulate", Color.GOLD, 0.3)

func _on_hit_pressed():
	if game_state == "playing":
		await deal_card_animated(player_hand, false)
		player_score = calculate_hand_value(player_hand)
		update_ui()

		if player_score > 21:
			end_game("💥 Bust! You lose! 😞")
		elif player_score == 21:
			dealer_turn()

func _on_stand_pressed():
	if game_state == "playing":
		dealer_turn()

func _on_new_game_pressed():
	start_new_game()
	ui_elements["message"].text = "🎲 New game started! Good luck! 🍀"
