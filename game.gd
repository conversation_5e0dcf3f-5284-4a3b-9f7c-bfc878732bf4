extends Node2D

var deck = []
var player_hand = []
var dealer_hand = []
var game_state = "waiting"
var player_score = 0
var dealer_score = 0
var player_balance = 1000
var current_bet = 0

var player_card_nodes = []
var dealer_card_nodes = []
var ui_elements = {}
var chip_nodes = []

var card_back_texture
var card_textures = {}
var suit_symbols = {"Hearts": "♥", "Diamonds": "♦", "Clubs": "♣", "Spades": "♠"}
var suit_colors = {"Hearts": Color.RED, "Diamonds": Color.RED, "Clubs": Color.BLACK, "Spades": Color.BLACK}

const CARD_SIZE = Vector2(100, 140)
const PLAYER_CARD_START = Vector2(400, 420)
const DEALER_CARD_START = Vector2(400, 180)
const CARD_SPACING = 110
const DECK_POSITION = Vector2(100, 100)

func _ready():
	get_viewport().size = Vector2(1200, 800)
	create_professional_card_textures()
	setup_casino_background()
	setup_professional_ui()
	create_deck()
	shuffle_deck()
	start_new_game()

func create_professional_card_textures():
	card_back_texture = create_professional_card_back()

	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]

	for suit in suits:
		for rank in ranks:
			var key = rank + "_" + suit
			card_textures[key] = create_professional_card_texture(rank, suit)

func create_professional_card_back():
	var image = Image.create(int(CARD_SIZE.x), int(CARD_SIZE.y), false, Image.FORMAT_RGBA8)

	var gradient_start = Color(0.12, 0.25, 0.69, 1.0)
	var gradient_end = Color(0.23, 0.51, 0.96, 1.0)

	for y in range(int(CARD_SIZE.y)):
		var t = float(y) / float(CARD_SIZE.y)
		var color = gradient_start.lerp(gradient_end, t)
		for x in range(int(CARD_SIZE.x)):
			image.set_pixel(x, y, color)

	draw_card_border(image, Color(0.8, 0.8, 0.8, 1.0))
	draw_card_back_pattern(image)

	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

func create_professional_card_texture(rank, suit):
	var image = Image.create(int(CARD_SIZE.x), int(CARD_SIZE.y), false, Image.FORMAT_RGBA8)

	var bg_gradient_start = Color(1.0, 1.0, 1.0, 1.0)
	var bg_gradient_end = Color(0.98, 0.98, 1.0, 1.0)

	for y in range(int(CARD_SIZE.y)):
		var t = float(y) / float(CARD_SIZE.y) * 0.1
		var color = bg_gradient_start.lerp(bg_gradient_end, t)
		for x in range(int(CARD_SIZE.x)):
			image.set_pixel(x, y, color)

	draw_card_border(image, Color(0.2, 0.2, 0.2, 1.0))
	draw_card_content(image, rank, suit)

	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

func draw_card_border(image: Image, border_color: Color):
	var width = int(CARD_SIZE.x)
	var height = int(CARD_SIZE.y)
	var border_width = 3
	var corner_radius = 8

	for i in range(border_width):
		for x in range(width):
			image.set_pixel(x, i, border_color)
			image.set_pixel(x, height - 1 - i, border_color)
		for y in range(height):
			image.set_pixel(i, y, border_color)
			image.set_pixel(width - 1 - i, y, border_color)

func draw_card_back_pattern(image: Image):
	var pattern_color = Color(1.0, 1.0, 1.0, 0.3)
	var center_x = int(CARD_SIZE.x / 2)
	var center_y = int(CARD_SIZE.y / 2)

	for y in range(center_y - 30, center_y + 30, 8):
		for x in range(center_x - 20, center_x + 20, 8):
			if x >= 0 and x < CARD_SIZE.x and y >= 0 and y < CARD_SIZE.y:
				for dy in range(4):
					for dx in range(4):
						if x + dx < CARD_SIZE.x and y + dy < CARD_SIZE.y:
							image.set_pixel(x + dx, y + dy, pattern_color)

func draw_card_content(image: Image, rank: String, suit: String):
	var suit_color = suit_colors[suit]
	var symbol = suit_symbols[suit]

	draw_rank_and_suit_corner(image, rank, symbol, suit_color, 15, 20, false)
	draw_rank_and_suit_corner(image, rank, symbol, suit_color, int(CARD_SIZE.x) - 25, int(CARD_SIZE.y) - 30, true)
	draw_center_suit(image, symbol, suit_color)

func draw_rank_and_suit_corner(image: Image, rank: String, symbol: String, color: Color, x: int, y: int, rotated: bool):
	draw_text_on_image(image, rank, x, y, color, 16)
	draw_text_on_image(image, symbol, x, y + 20, color, 14)

func draw_center_suit(image: Image, symbol: String, color: Color):
	var center_x = int(CARD_SIZE.x / 2)
	var center_y = int(CARD_SIZE.y / 2)
	draw_text_on_image(image, symbol, center_x - 8, center_y - 10, color, 24)

func draw_text_on_image(image: Image, text: String, x: int, y: int, color: Color, size: int):
	var char_width = size / 2
	var char_height = size

	for i in range(text.length()):
		var char_x = x + i * char_width
		if char_x + char_width < CARD_SIZE.x and y + char_height < CARD_SIZE.y:
			draw_char_pixels(image, text[i], char_x, y, color, size)

func draw_char_pixels(image: Image, character: String, x: int, y: int, color: Color, size: int):
	var pixel_size = max(1, size / 8)
	for dy in range(0, size, pixel_size):
		for dx in range(0, size / 2, pixel_size):
			if x + dx < CARD_SIZE.x and y + dy < CARD_SIZE.y:
				image.set_pixel(x + dx, y + dy, color)

func setup_casino_background():
	var screen_size = get_viewport().get_visible_rect().size

	var background = create_gradient_background(screen_size)
	add_child(background)

	var table = create_casino_table(screen_size)
	add_child(table)

	var felt_overlay = create_felt_texture(screen_size)
	add_child(felt_overlay)

func create_gradient_background(screen_size: Vector2) -> ColorRect:
	var background = ColorRect.new()
	background.size = screen_size
	background.color = Color(0.05, 0.15, 0.05, 1.0)
	return background

func create_casino_table(screen_size: Vector2) -> Control:
	var table_container = Control.new()
	table_container.size = screen_size

	var table = ColorRect.new()
	table.size = Vector2(screen_size.x - 80, 500)
	table.position = Vector2(40, screen_size.y / 2 - 250)
	table.color = Color(0.0, 0.4, 0.0, 1.0)
	table_container.add_child(table)

	var table_border = ColorRect.new()
	table_border.size = table.size + Vector2(8, 8)
	table_border.position = table.position - Vector2(4, 4)
	table_border.color = Color(0.6, 0.3, 0.0, 1.0)
	table_container.add_child(table_border)
	table_container.move_child(table_border, 0)

	return table_container

func create_felt_texture(screen_size: Vector2) -> Control:
	var felt_container = Control.new()
	felt_container.size = screen_size

	var felt_rect = ColorRect.new()
	felt_rect.size = Vector2(screen_size.x - 100, 480)
	felt_rect.position = Vector2(50, screen_size.y / 2 - 240)
	felt_rect.color = Color(0.0, 0.5, 0.0, 0.8)
	felt_container.add_child(felt_rect)

	return felt_container

func setup_professional_ui():
	var screen_size = get_viewport().get_visible_rect().size

	create_title_section(screen_size)
	create_dealer_section(screen_size)
	create_player_section(screen_size)
	create_betting_section(screen_size)
	create_action_buttons(screen_size)
	create_deck_display()

func create_title_section(screen_size: Vector2):
	var title = Label.new()
	title.text = "🎰 CASINO BLACKJACK 🎰"
	title.position = Vector2(screen_size.x / 2 - 250, 15)
	title.add_theme_font_size_override("font_size", 42)
	title.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))
	add_child(title)

func create_dealer_section(screen_size: Vector2):
	var dealer_label = Label.new()
	dealer_label.text = "DEALER"
	dealer_label.position = Vector2(50, 100)
	dealer_label.add_theme_font_size_override("font_size", 28)
	dealer_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(dealer_label)
	ui_elements["dealer_label"] = dealer_label

	var dealer_score_bg = ColorRect.new()
	dealer_score_bg.size = Vector2(120, 40)
	dealer_score_bg.position = Vector2(45, 135)
	dealer_score_bg.color = Color(0.0, 0.0, 0.0, 0.6)
	add_child(dealer_score_bg)

	var dealer_score_label = Label.new()
	dealer_score_label.text = "Score: ?"
	dealer_score_label.position = Vector2(55, 145)
	dealer_score_label.add_theme_font_size_override("font_size", 22)
	dealer_score_label.add_theme_color_override("font_color", Color(1.0, 1.0, 0.0, 1.0))
	add_child(dealer_score_label)
	ui_elements["dealer_score"] = dealer_score_label

func create_player_section(screen_size: Vector2):
	var player_label = Label.new()
	player_label.text = "PLAYER"
	player_label.position = Vector2(50, 380)
	player_label.add_theme_font_size_override("font_size", 28)
	player_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(player_label)
	ui_elements["player_label"] = player_label

	var player_score_bg = ColorRect.new()
	player_score_bg.size = Vector2(120, 40)
	player_score_bg.position = Vector2(45, 415)
	player_score_bg.color = Color(0.0, 0.0, 0.0, 0.6)
	add_child(player_score_bg)

	var player_score_label = Label.new()
	player_score_label.text = "Score: 0"
	player_score_label.position = Vector2(55, 425)
	player_score_label.add_theme_font_size_override("font_size", 22)
	player_score_label.add_theme_color_override("font_color", Color(1.0, 1.0, 0.0, 1.0))
	add_child(player_score_label)
	ui_elements["player_score"] = player_score_label

func create_betting_section(screen_size: Vector2):
	var balance_bg = ColorRect.new()
	balance_bg.size = Vector2(200, 50)
	balance_bg.position = Vector2(screen_size.x - 220, 20)
	balance_bg.color = Color(0.0, 0.0, 0.0, 0.7)
	add_child(balance_bg)

	var balance_label = Label.new()
	balance_label.text = "Balance: $" + str(player_balance)
	balance_label.position = Vector2(screen_size.x - 210, 35)
	balance_label.add_theme_font_size_override("font_size", 24)
	balance_label.add_theme_color_override("font_color", Color(0.0, 1.0, 0.0, 1.0))
	add_child(balance_label)
	ui_elements["balance"] = balance_label

	var bet_bg = ColorRect.new()
	bet_bg.size = Vector2(150, 40)
	bet_bg.position = Vector2(screen_size.x - 170, 80)
	bet_bg.color = Color(0.0, 0.0, 0.0, 0.7)
	add_child(bet_bg)

	var bet_label = Label.new()
	bet_label.text = "Bet: $" + str(current_bet)
	bet_label.position = Vector2(screen_size.x - 160, 90)
	bet_label.add_theme_font_size_override("font_size", 20)
	bet_label.add_theme_color_override("font_color", Color(1.0, 1.0, 0.0, 1.0))
	add_child(bet_label)
	ui_elements["bet"] = bet_label

	create_chip_buttons(screen_size)

func create_chip_buttons(screen_size: Vector2):
	var chip_values = [5, 10, 25, 50, 100]
	var chip_colors = [Color.RED, Color.BLUE, Color.GREEN, Color.PURPLE, Color.BLACK]

	for i in range(chip_values.size()):
		var chip_button = Button.new()
		chip_button.text = "$" + str(chip_values[i])
		chip_button.position = Vector2(screen_size.x - 600 + i * 70, screen_size.y - 120)
		chip_button.size = Vector2(60, 60)
		chip_button.add_theme_font_size_override("font_size", 14)
		chip_button.modulate = chip_colors[i]

		var chip_value = chip_values[i]
		chip_button.pressed.connect(_on_chip_pressed.bind(chip_value))
		add_child(chip_button)
		ui_elements["chip_" + str(chip_value)] = chip_button

func create_action_buttons(screen_size: Vector2):
	var hit_button = create_styled_button("🃏 HIT", Vector2(screen_size.x / 2 - 200, screen_size.y - 80))
	hit_button.pressed.connect(_on_hit_pressed)
	add_child(hit_button)
	ui_elements["hit_button"] = hit_button

	var stand_button = create_styled_button("✋ STAND", Vector2(screen_size.x / 2 - 60, screen_size.y - 80))
	stand_button.pressed.connect(_on_stand_pressed)
	add_child(stand_button)
	ui_elements["stand_button"] = stand_button

	var double_button = create_styled_button("⚡ DOUBLE", Vector2(screen_size.x / 2 + 80, screen_size.y - 80))
	double_button.pressed.connect(_on_double_pressed)
	add_child(double_button)
	ui_elements["double_button"] = double_button

	var deal_button = create_styled_button("🎯 DEAL", Vector2(screen_size.x / 2 + 220, screen_size.y - 80))
	deal_button.pressed.connect(_on_deal_pressed)
	add_child(deal_button)
	ui_elements["deal_button"] = deal_button

	var message_bg = ColorRect.new()
	message_bg.size = Vector2(600, 40)
	message_bg.position = Vector2(screen_size.x / 2 - 300, screen_size.y - 140)
	message_bg.color = Color(0.0, 0.0, 0.0, 0.7)
	add_child(message_bg)

	var message_label = Label.new()
	message_label.text = "🎲 Welcome to Casino Blackjack! Place your bet! 🎲"
	message_label.position = Vector2(screen_size.x / 2 - 290, screen_size.y - 130)
	message_label.add_theme_font_size_override("font_size", 20)
	message_label.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))
	add_child(message_label)
	ui_elements["message"] = message_label

func create_styled_button(text: String, pos: Vector2) -> Button:
	var button = Button.new()
	button.text = text
	button.position = pos
	button.size = Vector2(130, 60)
	button.add_theme_font_size_override("font_size", 16)
	return button

func create_deck_display():
	var deck_sprite = Sprite2D.new()
	deck_sprite.texture = card_back_texture
	deck_sprite.position = DECK_POSITION
	deck_sprite.scale = Vector2(0.6, 0.6)
	add_child(deck_sprite)
	ui_elements["deck_sprite"] = deck_sprite

	var deck_count_bg = ColorRect.new()
	deck_count_bg.size = Vector2(80, 30)
	deck_count_bg.position = Vector2(DECK_POSITION.x - 40, DECK_POSITION.y + 80)
	deck_count_bg.color = Color(0.0, 0.0, 0.0, 0.7)
	add_child(deck_count_bg)

	var deck_count_label = Label.new()
	deck_count_label.text = str(deck.size())
	deck_count_label.position = Vector2(DECK_POSITION.x - 20, DECK_POSITION.y + 85)
	deck_count_label.add_theme_font_size_override("font_size", 18)
	deck_count_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(deck_count_label)
	ui_elements["deck_count"] = deck_count_label

func create_deck():
	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]

	deck.clear()

	for suit in suits:
		for rank in ranks:
			var card = {
				"suit": suit,
				"rank": rank
			}
			deck.append(card)

func shuffle_deck():
	deck.shuffle()

func create_professional_card_node(card, is_face_down = false):
	var card_container = Control.new()
	card_container.size = CARD_SIZE

	var card_sprite = Sprite2D.new()
	card_sprite.scale = Vector2(0.9, 0.9)
	card_sprite.position = CARD_SIZE / 2

	if is_face_down:
		card_sprite.texture = card_back_texture
		card_container.set_meta("is_face_down", true)
	else:
		var key = card["rank"] + "_" + card["suit"]
		card_sprite.texture = card_textures[key]
		card_container.set_meta("is_face_down", false)

	card_container.set_meta("card_data", card)
	card_container.add_child(card_sprite)

	var shadow = ColorRect.new()
	shadow.size = CARD_SIZE + Vector2(4, 4)
	shadow.position = Vector2(2, 2)
	shadow.color = Color(0.0, 0.0, 0.0, 0.3)
	shadow.z_index = -1
	card_container.add_child(shadow)
	card_container.move_child(shadow, 0)

	add_child(card_container)
	return card_container

func animate_professional_card_deal(card_node, target_position):
	var start_position = DECK_POSITION
	card_node.position = start_position
	card_node.scale = Vector2(0.6, 0.6)
	card_node.rotation = deg_to_rad(180)
	card_node.modulate.a = 0.0

	var tween = create_tween()
	tween.set_parallel(true)

	tween.tween_property(card_node, "position", target_position, 0.8)
	tween.tween_property(card_node, "scale", Vector2(1.0, 1.0), 0.8)
	tween.tween_property(card_node, "rotation", 0.0, 0.8)
	tween.tween_property(card_node, "modulate:a", 1.0, 0.4)

	tween.tween_method(add_card_trail, start_position, target_position, 0.8)

	await tween.finished

	var bounce_tween = create_tween()
	bounce_tween.tween_property(card_node, "scale", Vector2(1.05, 1.05), 0.1)
	bounce_tween.tween_property(card_node, "scale", Vector2(1.0, 1.0), 0.1)

func add_card_trail(position: Vector2):
	var trail_particle = ColorRect.new()
	trail_particle.size = Vector2(4, 4)
	trail_particle.position = position
	trail_particle.color = Color(1.0, 1.0, 1.0, 0.5)
	add_child(trail_particle)

	var fade_tween = create_tween()
	fade_tween.tween_property(trail_particle, "modulate:a", 0.0, 0.3)
	fade_tween.tween_callback(trail_particle.queue_free)

func clear_all_cards():
	for card_node in player_card_nodes:
		if card_node and is_instance_valid(card_node):
			animate_card_removal(card_node)
	for card_node in dealer_card_nodes:
		if card_node and is_instance_valid(card_node):
			animate_card_removal(card_node)

	player_card_nodes.clear()
	dealer_card_nodes.clear()

func animate_card_removal(card_node):
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(card_node, "scale", Vector2(0.0, 0.0), 0.3)
	tween.tween_property(card_node, "modulate:a", 0.0, 0.3)
	tween.tween_property(card_node, "rotation", deg_to_rad(180), 0.3)
	tween.tween_callback(card_node.queue_free)

func start_new_game():
	if current_bet <= 0:
		show_message("Please place a bet first!")
		return

	if player_balance < current_bet:
		show_message("Insufficient balance!")
		return

	player_balance -= current_bet
	clear_all_cards()
	player_hand.clear()
	dealer_hand.clear()
	game_state = "dealing"

	update_ui()
	show_message("Dealing cards...")

	if deck.size() < 10:
		create_deck()
		shuffle_deck()

	await deal_initial_cards()

	game_state = "playing"
	update_ui()
	check_blackjack()

func deal_initial_cards():
	await deal_card_animated(player_hand, false)
	await get_tree().create_timer(0.3).timeout
	await deal_card_animated(dealer_hand, true)
	await get_tree().create_timer(0.3).timeout
	await deal_card_animated(player_hand, false)
	await get_tree().create_timer(0.3).timeout
	await deal_card_animated(dealer_hand, false)

func deal_card(hand):
	if deck.size() > 0:
		var card = deck.pop_back()
		hand.append(card)
		return card
	return null

func deal_card_animated(hand, is_dealer_first_card = false):
	if deck.size() > 0:
		var card = deck.pop_back()
		hand.append(card)

		var is_face_down = is_dealer_first_card and hand == dealer_hand and dealer_hand.size() == 1
		var card_node = create_professional_card_node(card, is_face_down)

		var target_position
		if hand == player_hand:
			target_position = PLAYER_CARD_START + Vector2(CARD_SPACING * (player_hand.size() - 1), 0)
			player_card_nodes.append(card_node)
		else:
			target_position = DEALER_CARD_START + Vector2(CARD_SPACING * (dealer_hand.size() - 1), 0)
			dealer_card_nodes.append(card_node)

		await animate_professional_card_deal(card_node, target_position)
		update_deck_count()
		return card
	return null

func flip_dealer_first_card():
	if dealer_card_nodes.size() > 0 and dealer_hand.size() > 0:
		var first_card_node = dealer_card_nodes[0]
		var first_card = dealer_hand[0]

		show_message("Revealing dealer's card...")

		var card_sprite = first_card_node.get_child(1)

		var flip_tween = create_tween()
		flip_tween.set_parallel(true)
		flip_tween.tween_property(card_sprite, "scale:x", 0.0, 0.3)
		flip_tween.tween_property(first_card_node, "rotation", deg_to_rad(10), 0.15)
		flip_tween.tween_property(first_card_node, "rotation", 0.0, 0.15)

		await flip_tween.finished

		var key = first_card["rank"] + "_" + first_card["suit"]
		card_sprite.texture = card_textures[key]
		first_card_node.set_meta("is_face_down", false)

		var reveal_tween = create_tween()
		reveal_tween.set_parallel(true)
		reveal_tween.tween_property(card_sprite, "scale:x", 0.9, 0.3)

		create_flip_effect(first_card_node.position)

func create_flip_effect(position: Vector2):
	for i in range(8):
		var particle = ColorRect.new()
		particle.size = Vector2(6, 6)
		particle.position = position + Vector2(randf_range(-20, 20), randf_range(-20, 20))
		particle.color = Color(1.0, 1.0, 0.0, 0.8)
		add_child(particle)

		var particle_tween = create_tween()
		particle_tween.set_parallel(true)
		particle_tween.tween_property(particle, "position", particle.position + Vector2(randf_range(-50, 50), randf_range(-50, 50)), 0.5)
		particle_tween.tween_property(particle, "modulate:a", 0.0, 0.5)
		particle_tween.tween_callback(particle.queue_free)

func update_deck_count():
	if ui_elements.has("deck_count"):
		ui_elements["deck_count"].text = str(deck.size())

func get_card_value(card):
	var rank = card["rank"]
	if rank in ["J", "Q", "K"]:
		return 10
	elif rank == "A":
		return 11
	else:
		return int(rank)

func calculate_hand_value(hand):
	var total = 0
	var aces = 0

	for card in hand:
		var value = get_card_value(card)
		if card["rank"] == "A":
			aces += 1
		total += value

	while total > 21 and aces > 0:
		total -= 10
		aces -= 1

	return total

func update_ui():
	player_score = calculate_hand_value(player_hand)

	ui_elements["player_score"].text = "Score: " + str(player_score)
	ui_elements["balance"].text = "Balance: $" + str(player_balance)
	ui_elements["bet"].text = "Bet: $" + str(current_bet)

	if game_state == "playing" or game_state == "dealing":
		ui_elements["dealer_score"].text = "Score: ?"
	else:
		dealer_score = calculate_hand_value(dealer_hand)
		ui_elements["dealer_score"].text = "Score: " + str(dealer_score)

	update_button_states()
	update_score_colors()

func update_button_states():
	var can_play = (game_state == "playing")
	var can_bet = (game_state == "waiting")
	var can_deal = (current_bet > 0 and game_state == "waiting")

	ui_elements["hit_button"].disabled = not can_play
	ui_elements["stand_button"].disabled = not can_play
	ui_elements["double_button"].disabled = not (can_play and player_hand.size() == 2 and player_balance >= current_bet)
	ui_elements["deal_button"].disabled = not can_deal

	for i in [5, 10, 25, 50, 100]:
		if ui_elements.has("chip_" + str(i)):
			ui_elements["chip_" + str(i)].disabled = not (can_bet and player_balance >= i)

func update_score_colors():
	if player_score > 21:
		ui_elements["player_score"].add_theme_color_override("font_color", Color.RED)
		create_bust_effect(PLAYER_CARD_START)
	elif player_score == 21:
		ui_elements["player_score"].add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))
		if player_hand.size() == 2:
			create_blackjack_effect(PLAYER_CARD_START)
	else:
		ui_elements["player_score"].add_theme_color_override("font_color", Color(1.0, 1.0, 0.0, 1.0))

func create_bust_effect(position: Vector2):
	var bust_label = Label.new()
	bust_label.text = "BUST!"
	bust_label.position = position + Vector2(50, -50)
	bust_label.add_theme_font_size_override("font_size", 36)
	bust_label.add_theme_color_override("font_color", Color.RED)
	add_child(bust_label)

	var effect_tween = create_tween()
	effect_tween.set_parallel(true)
	effect_tween.tween_property(bust_label, "position", bust_label.position + Vector2(0, -30), 1.0)
	effect_tween.tween_property(bust_label, "modulate:a", 0.0, 1.0)
	effect_tween.tween_callback(bust_label.queue_free)

func create_blackjack_effect(position: Vector2):
	var bj_label = Label.new()
	bj_label.text = "BLACKJACK!"
	bj_label.position = position + Vector2(30, -50)
	bj_label.add_theme_font_size_override("font_size", 32)
	bj_label.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))
	add_child(bj_label)

	var effect_tween = create_tween()
	effect_tween.set_parallel(true)
	effect_tween.tween_property(bj_label, "scale", Vector2(1.2, 1.2), 0.3)
	effect_tween.tween_property(bj_label, "scale", Vector2(1.0, 1.0), 0.3)
	effect_tween.tween_property(bj_label, "position", bj_label.position + Vector2(0, -30), 2.0)
	effect_tween.tween_property(bj_label, "modulate:a", 0.0, 2.0)
	effect_tween.tween_callback(bj_label.queue_free)

func show_message(text: String):
	ui_elements["message"].text = text

	var message_tween = create_tween()
	message_tween.tween_property(ui_elements["message"], "modulate", Color(1.0, 1.0, 0.0, 1.0), 0.2)
	message_tween.tween_property(ui_elements["message"], "modulate", Color(1.0, 0.84, 0.0, 1.0), 0.2)

func check_blackjack():
	if player_score == 21 and player_hand.size() == 2:
		if dealer_score == 21 and dealer_hand.size() == 2:
			end_game("Push! Both have Blackjack!")
		else:
			end_game("Blackjack! You win!")
	elif dealer_score == 21 and dealer_hand.size() == 2:
		end_game("Dealer has Blackjack! You lose!")

func dealer_turn():
	game_state = "dealer_turn"
	await flip_dealer_first_card()
	update_ui()

	await get_tree().create_timer(1.0).timeout

	while calculate_hand_value(dealer_hand) < 17:
		show_message("Dealer hits...")
		await get_tree().create_timer(1.0).timeout
		await deal_card_animated(dealer_hand, false)
		update_ui()
		await get_tree().create_timer(0.5).timeout

	determine_winner()

func determine_winner():
	var final_dealer_score = calculate_hand_value(dealer_hand)
	var final_player_score = calculate_hand_value(player_hand)

	var winnings = 0
	var message = ""

	if final_dealer_score > 21:
		winnings = current_bet * 2
		message = "💥 Dealer busts! You win! 🎉"
	elif final_player_score > 21:
		winnings = 0
		message = "💥 Bust! You lose! 😞"
	elif final_player_score == 21 and player_hand.size() == 2:
		winnings = int(current_bet * 2.5)
		message = "🃏 BLACKJACK! You win! 🎉"
	elif final_dealer_score > final_player_score:
		winnings = 0
		message = "😞 Dealer wins! Better luck next time!"
	elif final_player_score > final_dealer_score:
		winnings = current_bet * 2
		message = "🎉 You win! Congratulations! 🎉"
	else:
		winnings = current_bet
		message = "🤝 Push! It's a tie!"

	player_balance += winnings
	if winnings > current_bet:
		animate_chip_win(winnings - current_bet)

	end_game(message)

func animate_chip_win(amount: int):
	var chip_label = Label.new()
	chip_label.text = "+$" + str(amount)
	chip_label.position = Vector2(get_viewport().size.x - 200, 100)
	chip_label.add_theme_font_size_override("font_size", 28)
	chip_label.add_theme_color_override("font_color", Color.GREEN)
	add_child(chip_label)

	var win_tween = create_tween()
	win_tween.set_parallel(true)
	win_tween.tween_property(chip_label, "position", chip_label.position + Vector2(0, -50), 2.0)
	win_tween.tween_property(chip_label, "modulate:a", 0.0, 2.0)
	win_tween.tween_callback(chip_label.queue_free)

func end_game(message: String):
	game_state = "waiting"
	current_bet = 0
	show_message(message)
	update_ui()

	var celebration_tween = create_tween()
	celebration_tween.set_loops(3)
	celebration_tween.tween_property(ui_elements["message"], "modulate", Color.YELLOW, 0.3)
	celebration_tween.tween_property(ui_elements["message"], "modulate", Color(1.0, 0.84, 0.0, 1.0), 0.3)

func _on_chip_pressed(value: int):
	if game_state == "waiting" and player_balance >= value:
		current_bet += value
		player_balance -= value
		animate_chip_to_bet(value)
		update_ui()
		show_message("Bet placed: $" + str(current_bet))

func animate_chip_to_bet(value: int):
	var chip_sprite = ColorRect.new()
	chip_sprite.size = Vector2(40, 40)
	chip_sprite.position = Vector2(get_viewport().size.x - 600 + ([5, 10, 25, 50, 100].find(value)) * 70, get_viewport().size.y - 120)
	chip_sprite.color = [Color.RED, Color.BLUE, Color.GREEN, Color.PURPLE, Color.BLACK][[5, 10, 25, 50, 100].find(value)]
	add_child(chip_sprite)

	var target_pos = Vector2(get_viewport().size.x - 100, 100)
	var chip_tween = create_tween()
	chip_tween.set_parallel(true)
	chip_tween.tween_property(chip_sprite, "position", target_pos, 0.5)
	chip_tween.tween_property(chip_sprite, "scale", Vector2(0.5, 0.5), 0.5)
	chip_tween.tween_callback(chip_sprite.queue_free)

func _on_hit_pressed():
	if game_state == "playing":
		show_message("You hit!")
		await deal_card_animated(player_hand, false)
		player_score = calculate_hand_value(player_hand)
		update_ui()

		if player_score > 21:
			determine_winner()
		elif player_score == 21:
			show_message("21! Standing...")
			await get_tree().create_timer(1.0).timeout
			dealer_turn()

func _on_stand_pressed():
	if game_state == "playing":
		show_message("You stand!")
		await get_tree().create_timer(0.5).timeout
		dealer_turn()

func _on_double_pressed():
	if game_state == "playing" and player_hand.size() == 2 and player_balance >= current_bet:
		player_balance -= current_bet
		current_bet *= 2
		show_message("Double down!")
		await deal_card_animated(player_hand, false)
		player_score = calculate_hand_value(player_hand)
		update_ui()

		if player_score > 21:
			determine_winner()
		else:
			await get_tree().create_timer(1.0).timeout
			dealer_turn()

func _on_deal_pressed():
	if current_bet > 0 and game_state == "waiting":
		start_new_game()
