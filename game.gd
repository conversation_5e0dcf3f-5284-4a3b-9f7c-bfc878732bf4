extends Node2D

# 游戏状态变量
var deck = []
var player_hand = []
var dealer_hand = []
var game_state = "waiting"  # waiting, playing, dealer_turn, game_over
var player_score = 0
var dealer_score = 0
var player_balance = 1000
var current_bet = 0

# UI节点存储
var ui_elements = {}
var player_card_nodes = []
var dealer_card_nodes = []

# 游戏常量 - 完全还原blackjack-online的布局
const CARD_SIZE = Vector2(70, 95)
const PLAYER_CARD_START = Vector2(400, 450)
const DEALER_CARD_START = Vector2(400, 200)
const CARD_SPACING = 80

func _ready():
	# 设置窗口大小
	get_viewport().size = Vector2(1200, 800)
	
	# 创建完全还原blackjack-online的UI
	setup_casino_ui()
	
	# 初始化游戏
	create_deck()
	shuffle_deck()
	game_state = "waiting"
	update_ui()

func setup_casino_ui():
	var screen_size = get_viewport().get_visible_rect().size
	
	# 创建背景 - 对应body样式
	create_background(screen_size)
	
	# 创建顶部状态栏 - 对应.top-status
	create_top_status(screen_size)
	
	# 创建牌堆区域 - 对应.deck-area
	create_deck_area(screen_size)
	
	# 创建赌桌 - 对应.casino-table
	create_casino_table(screen_size)
	
	# 创建庄家区域 - 对应.dealer-section
	create_dealer_section(screen_size)
	
	# 创建玩家区域 - 对应.players-area
	create_players_area(screen_size)
	
	# 创建游戏控制区域 - 对应.game-actions-section
	create_game_actions(screen_size)
	
	# 创建筹码区域 - 对应.chip-section
	create_chip_section(screen_size)

func create_background(screen_size: Vector2):
	# 主背景容器 - 对应.casino-container
	var casino_container = Control.new()
	casino_container.name = "CasinoContainer"
	casino_container.size = screen_size
	casino_container.position = Vector2.ZERO
	add_child(casino_container)
	ui_elements["casino_container"] = casino_container
	
	# 背景图片 - 对应body background
	var background = ColorRect.new()
	background.name = "Background"
	background.size = screen_size
	background.position = Vector2.ZERO
	background.color = Color(0.05, 0.2, 0.05, 1.0)  # 深绿色赌场背景
	casino_container.add_child(background)
	ui_elements["background"] = background

func create_top_status(screen_size: Vector2):
	# 顶部状态栏 - 对应.top-status
	var top_status = Control.new()
	top_status.name = "TopStatus"
	top_status.size = Vector2(screen_size.x, 60)
	top_status.position = Vector2(0, 10)
	ui_elements["casino_container"].add_child(top_status)
	
	# 右侧控制按钮 - 对应.right-controls
	var right_controls = Control.new()
	right_controls.name = "RightControls"
	right_controls.size = Vector2(200, 50)
	right_controls.position = Vector2(screen_size.x - 220, 5)
	top_status.add_child(right_controls)
	
	# 设置按钮 - 对应#settings-button
	var settings_button = Button.new()
	settings_button.name = "SettingsButton"
	settings_button.text = "⚙️"
	settings_button.size = Vector2(50, 40)
	settings_button.position = Vector2(150, 5)
	settings_button.add_theme_font_size_override("font_size", 20)
	right_controls.add_child(settings_button)
	ui_elements["settings_button"] = settings_button

func create_deck_area(screen_size: Vector2):
	# 牌堆区域 - 对应.deck-area
	var deck_area = Control.new()
	deck_area.name = "DeckArea"
	deck_area.size = Vector2(500, 100)
	deck_area.position = Vector2((screen_size.x - 500) / 2, 10)
	ui_elements["casino_container"].add_child(deck_area)
	
	# 主牌堆 - 对应#main-deck
	var main_deck = Control.new()
	main_deck.name = "MainDeck"
	main_deck.size = Vector2(100, 100)
	main_deck.position = Vector2(0, 0)
	deck_area.add_child(main_deck)
	
	# 牌堆卡牌 - 对应.deck-card
	var deck_card = create_card_back()
	deck_card.name = "DeckCard"
	deck_card.size = Vector2(60, 84)
	deck_card.position = Vector2(20, 8)
	main_deck.add_child(deck_card)
	
	# 牌堆信息 - 对应.deck-info
	var deck_info = Control.new()
	deck_info.name = "DeckInfo"
	deck_info.size = Vector2(100, 40)
	deck_info.position = Vector2(0, 60)
	main_deck.add_child(deck_info)
	
	# 牌数标签 - 对应.deck-label
	var deck_label = Label.new()
	deck_label.name = "DeckLabel"
	deck_label.text = "Cards Left"
	deck_label.size = Vector2(100, 15)
	deck_label.position = Vector2(0, 0)
	deck_label.add_theme_font_size_override("font_size", 10)
	deck_label.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))  # #ffd700
	deck_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	deck_info.add_child(deck_label)
	
	# 牌数显示 - 对应#deck-count
	var deck_count = Label.new()
	deck_count.name = "DeckCount"
	deck_count.text = "52"
	deck_count.size = Vector2(100, 20)
	deck_count.position = Vector2(0, 15)
	deck_count.add_theme_font_size_override("font_size", 14)
	deck_count.add_theme_color_override("font_color", Color.WHITE)
	deck_count.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	deck_info.add_child(deck_count)
	ui_elements["deck_count"] = deck_count

func create_card_back() -> ColorRect:
	# 创建卡牌背面 - 对应.card-back样式
	var card_back = ColorRect.new()
	card_back.color = Color(0.12, 0.25, 0.69, 1.0)  # 蓝色渐变起始色
	
	# 添加边框效果
	var border = ColorRect.new()
	border.size = card_back.size + Vector2(4, 4)
	border.position = Vector2(-2, -2)
	border.color = Color(0.2, 0.2, 0.2, 1.0)
	border.z_index = -1
	card_back.add_child(border)
	
	return card_back

func create_casino_table(screen_size: Vector2):
	# 赌桌容器 - 对应.casino-table
	var casino_table = Control.new()
	casino_table.name = "CasinoTable"
	casino_table.size = Vector2(screen_size.x, screen_size.y - 120)
	casino_table.position = Vector2(0, 120)
	ui_elements["casino_container"].add_child(casino_table)
	ui_elements["casino_table"] = casino_table

func create_dealer_section(screen_size: Vector2):
	# 庄家区域 - 对应.dealer-section
	var dealer_section = Control.new()
	dealer_section.name = "DealerSection"
	dealer_section.size = Vector2(screen_size.x, 200)
	dealer_section.position = Vector2(0, 0)
	ui_elements["casino_table"].add_child(dealer_section)
	ui_elements["dealer_section"] = dealer_section
	
	# 庄家信息 - 对应.dealer-info
	var dealer_info = Control.new()
	dealer_info.name = "DealerInfo"
	dealer_info.size = Vector2(200, 80)
	dealer_info.position = Vector2((screen_size.x - 200) / 2, 20)
	dealer_section.add_child(dealer_info)
	
	# 庄家标题 - 对应.dealer-title
	var dealer_title = Label.new()
	dealer_title.name = "DealerTitle"
	dealer_title.text = "DEALER"
	dealer_title.size = Vector2(200, 30)
	dealer_title.position = Vector2(0, 0)
	dealer_title.add_theme_font_size_override("font_size", 16)
	dealer_title.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))  # #ffd700
	dealer_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	dealer_info.add_child(dealer_title)
	
	# 庄家分数 - 对应.dealer-score
	var dealer_score = Label.new()
	dealer_score.name = "DealerScore"
	dealer_score.text = "0"
	dealer_score.size = Vector2(200, 40)
	dealer_score.position = Vector2(0, 30)
	dealer_score.add_theme_font_size_override("font_size", 24)
	dealer_score.add_theme_color_override("font_color", Color.WHITE)
	dealer_score.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	dealer_info.add_child(dealer_score)
	ui_elements["dealer_score"] = dealer_score
	
	# 庄家卡牌容器 - 对应.dealer-cards-container
	var dealer_cards_container = Control.new()
	dealer_cards_container.name = "DealerCardsContainer"
	dealer_cards_container.size = Vector2(screen_size.x, 120)
	dealer_cards_container.position = Vector2(0, 80)
	dealer_section.add_child(dealer_cards_container)
	
	# 庄家卡牌区域 - 对应#dealer-cards
	var dealer_cards = Control.new()
	dealer_cards.name = "DealerCards"
	dealer_cards.size = Vector2(600, 95)
	dealer_cards.position = Vector2((screen_size.x - 600) / 2, 12)
	dealer_cards_container.add_child(dealer_cards)
	ui_elements["dealer_cards"] = dealer_cards

func create_players_area(screen_size: Vector2):
	# 玩家区域 - 对应.players-area
	var players_area = Control.new()
	players_area.name = "PlayersArea"
	players_area.size = Vector2(screen_size.x, 300)
	players_area.position = Vector2(0, 200)
	ui_elements["casino_table"].add_child(players_area)
	ui_elements["players_area"] = players_area

	# 玩家位置 - 对应.player-position.current-player
	var player_position = Control.new()
	player_position.name = "PlayerPosition"
	player_position.size = Vector2(400, 300)
	player_position.position = Vector2((screen_size.x - 400) / 2, 0)
	players_area.add_child(player_position)
	ui_elements["player_position"] = player_position

	# 玩家卡牌容器 - 对应.player-cards-container
	var player_cards_container = Control.new()
	player_cards_container.name = "PlayerCardsContainer"
	player_cards_container.size = Vector2(400, 150)
	player_cards_container.position = Vector2(0, 0)
	player_position.add_child(player_cards_container)

	# 玩家卡牌区域 - 对应#player-cards-0
	var player_cards = Control.new()
	player_cards.name = "PlayerCards"
	player_cards.size = Vector2(400, 95)
	player_cards.position = Vector2(0, 0)
	player_cards_container.add_child(player_cards)
	ui_elements["player_cards"] = player_cards

	# 玩家分数 - 对应.player-score
	var player_score = Label.new()
	player_score.name = "PlayerScore"
	player_score.text = "0"
	player_score.size = Vector2(100, 30)
	player_score.position = Vector2(150, 100)
	player_score.add_theme_font_size_override("font_size", 20)
	player_score.add_theme_color_override("font_color", Color.WHITE)
	player_score.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	player_cards_container.add_child(player_score)
	ui_elements["player_score"] = player_score

	# 投注区域 - 对应.bet-spot
	var bet_spot = Control.new()
	bet_spot.name = "BetSpot"
	bet_spot.size = Vector2(100, 100)
	bet_spot.position = Vector2(150, 150)
	player_position.add_child(bet_spot)

	# 投注圆圈 - 对应.bet-circle
	var bet_circle = create_bet_circle()
	bet_circle.name = "BetCircle"
	bet_circle.size = Vector2(80, 80)
	bet_circle.position = Vector2(10, 10)
	bet_spot.add_child(bet_circle)
	ui_elements["bet_circle"] = bet_circle

	# 投注金额显示 - 对应#bet-amount-0
	var bet_amount = Label.new()
	bet_amount.name = "BetAmount"
	bet_amount.text = "0"
	bet_amount.size = Vector2(80, 80)
	bet_amount.position = Vector2(0, 0)
	bet_amount.add_theme_font_size_override("font_size", 16)
	bet_amount.add_theme_color_override("font_color", Color.WHITE)
	bet_amount.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	bet_amount.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	bet_circle.add_child(bet_amount)
	ui_elements["bet_amount"] = bet_amount

func create_bet_circle() -> ColorRect:
	# 创建投注圆圈 - 对应.bet-circle样式
	var bet_circle = ColorRect.new()
	bet_circle.color = Color(0.0, 0.0, 0.0, 0.6)  # 半透明黑色

	# 添加边框
	var border = ColorRect.new()
	border.size = bet_circle.size + Vector2(4, 4)
	border.position = Vector2(-2, -2)
	border.color = Color(1.0, 0.84, 0.0, 0.8)  # 金色边框
	border.z_index = -1
	bet_circle.add_child(border)

	return bet_circle

func create_game_actions(screen_size: Vector2):
	# 游戏操作区域 - 对应.game-actions-section
	var game_actions = Control.new()
	game_actions.name = "GameActions"
	game_actions.size = Vector2(400, 80)
	game_actions.position = Vector2((screen_size.x - 400) / 2, screen_size.y - 160)
	ui_elements["casino_container"].add_child(game_actions)
	ui_elements["game_actions"] = game_actions

	# 操作控制容器 - 对应.action-controls
	var action_controls = Control.new()
	action_controls.name = "ActionControls"
	action_controls.size = Vector2(400, 80)
	action_controls.position = Vector2(0, 0)
	game_actions.add_child(action_controls)

	# Hit按钮 - 对应#hit
	var hit_button = create_action_button("🃏", "Hit")
	hit_button.name = "HitButton"
	hit_button.position = Vector2(50, 10)
	hit_button.pressed.connect(_on_hit_pressed)
	action_controls.add_child(hit_button)
	ui_elements["hit_button"] = hit_button

	# Stand按钮 - 对应#stand
	var stand_button = create_action_button("✋", "Stand")
	stand_button.name = "StandButton"
	stand_button.position = Vector2(130, 10)
	stand_button.pressed.connect(_on_stand_pressed)
	action_controls.add_child(stand_button)
	ui_elements["stand_button"] = stand_button

	# Double按钮 - 对应#double-down
	var double_button = create_action_button("⚡", "Double")
	double_button.name = "DoubleButton"
	double_button.position = Vector2(210, 10)
	double_button.pressed.connect(_on_double_pressed)
	action_controls.add_child(double_button)
	ui_elements["double_button"] = double_button

	# Surrender按钮 - 对应#surrender
	var surrender_button = create_action_button("🏳️", "Surrender")
	surrender_button.name = "SurrenderButton"
	surrender_button.position = Vector2(290, 10)
	surrender_button.pressed.connect(_on_surrender_pressed)
	action_controls.add_child(surrender_button)
	ui_elements["surrender_button"] = surrender_button

func create_action_button(icon: String, text: String) -> Button:
	# 创建操作按钮 - 对应.action-btn样式
	var button = Button.new()
	button.size = Vector2(60, 60)
	button.text = icon
	button.add_theme_font_size_override("font_size", 20)

	# 添加按钮文本标签
	var btn_text = Label.new()
	btn_text.text = text
	btn_text.size = Vector2(60, 20)
	btn_text.position = Vector2(0, 65)
	btn_text.add_theme_font_size_override("font_size", 10)
	btn_text.add_theme_color_override("font_color", Color.WHITE)
	btn_text.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	button.add_child(btn_text)

	return button

func create_chip_section(screen_size: Vector2):
	# 筹码区域 - 对应.chip-section
	var chip_section = Control.new()
	chip_section.name = "ChipSection"
	chip_section.size = Vector2(screen_size.x, 80)
	chip_section.position = Vector2(0, screen_size.y - 80)
	ui_elements["casino_container"].add_child(chip_section)
	ui_elements["chip_section"] = chip_section

	# 玩家余额显示 - 对应.player-balance
	var player_balance_label = Label.new()
	player_balance_label.name = "PlayerBalance"
	player_balance_label.text = str(player_balance)
	player_balance_label.size = Vector2(150, 30)
	player_balance_label.position = Vector2(50, 10)
	player_balance_label.add_theme_font_size_override("font_size", 24)
	player_balance_label.add_theme_color_override("font_color", Color(0.0, 1.0, 0.0, 1.0))  # 绿色
	chip_section.add_child(player_balance_label)
	ui_elements["player_balance"] = player_balance_label

	# 筹码托盘 - 对应.chip-tray
	var chip_tray = Control.new()
	chip_tray.name = "ChipTray"
	chip_tray.size = Vector2(400, 60)
	chip_tray.position = Vector2((screen_size.x - 400) / 2, 10)
	chip_section.add_child(chip_tray)
	ui_elements["chip_tray"] = chip_tray

	# 创建筹码按钮
	create_chip_buttons(chip_tray)

	# 投注操作区域 - 对应.betting-section
	var betting_section = Control.new()
	betting_section.name = "BettingSection"
	betting_section.size = Vector2(200, 60)
	betting_section.position = Vector2(screen_size.x - 250, 10)
	chip_section.add_child(betting_section)

	# Clear按钮 - 对应#clear-bet
	var clear_button = Button.new()
	clear_button.name = "ClearButton"
	clear_button.text = "Clear"
	clear_button.size = Vector2(60, 40)
	clear_button.position = Vector2(0, 10)
	clear_button.pressed.connect(_on_clear_bet_pressed)
	betting_section.add_child(clear_button)
	ui_elements["clear_button"] = clear_button

	# Deal按钮 - 对应#deal-cards
	var deal_button = Button.new()
	deal_button.name = "DealButton"
	deal_button.text = "Deal"
	deal_button.size = Vector2(60, 40)
	deal_button.position = Vector2(70, 10)
	deal_button.pressed.connect(_on_deal_pressed)
	betting_section.add_child(deal_button)
	ui_elements["deal_button"] = deal_button

func create_chip_buttons(chip_tray: Control):
	# 创建不同面额的筹码按钮
	var chip_values = [1, 5, 10, 25, 50, 100]
	var chip_spacing = 60

	for i in range(chip_values.size()):
		var chip_btn = create_chip_button(chip_values[i])
		chip_btn.position = Vector2(i * chip_spacing + 10, 5)
		chip_tray.add_child(chip_btn)
		ui_elements["chip_" + str(chip_values[i])] = chip_btn

func create_chip_button(value: int) -> Button:
	# 创建筹码按钮 - 对应.chip-btn样式
	var chip_btn = Button.new()
	chip_btn.size = Vector2(50, 50)
	chip_btn.text = str(value)
	chip_btn.add_theme_font_size_override("font_size", 12)

	# 根据面额设置颜色
	var chip_colors = {
		1: Color.WHITE,
		5: Color.RED,
		10: Color.BLUE,
		25: Color.GREEN,
		50: Color.PURPLE,
		100: Color.BLACK
	}

	chip_btn.modulate = chip_colors.get(value, Color.GRAY)
	chip_btn.pressed.connect(_on_chip_pressed.bind(value))

	return chip_btn

# 游戏核心逻辑函数
func create_deck():
	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]

	deck.clear()

	for suit in suits:
		for rank in ranks:
			var card = {
				"suit": suit,
				"rank": rank,
				"value": rank,
				"color": "red" if suit in ["Hearts", "Diamonds"] else "black"
			}
			deck.append(card)

func shuffle_deck():
	deck.shuffle()

func get_card_value(card):
	var rank = card["rank"]
	if rank in ["J", "Q", "K"]:
		return 10
	elif rank == "A":
		return 11
	else:
		return int(rank)

func calculate_hand_value(hand):
	var total = 0
	var aces = 0

	for card in hand:
		var value = get_card_value(card)
		if card["rank"] == "A":
			aces += 1
		total += value

	while total > 21 and aces > 0:
		total -= 10
		aces -= 1

	return total

func deal_card():
	if deck.size() > 0:
		return deck.pop_back()
	else:
		create_deck()
		shuffle_deck()
		return deck.pop_back()

func create_card_element(card, is_hidden = false):
	# 创建卡牌元素 - 对应.card样式
	var card_element = Control.new()
	card_element.size = CARD_SIZE

	if is_hidden:
		# 创建卡牌背面
		var card_back = create_card_back()
		card_back.size = CARD_SIZE
		card_element.add_child(card_back)
	else:
		# 创建卡牌正面
		var card_front = ColorRect.new()
		card_front.size = CARD_SIZE
		card_front.color = Color.WHITE

		# 添加边框
		var border = ColorRect.new()
		border.size = CARD_SIZE + Vector2(2, 2)
		border.position = Vector2(-1, -1)
		border.color = Color.BLACK
		border.z_index = -1
		card_front.add_child(border)

		# 添加卡牌内容
		var card_content = Label.new()
		card_content.text = card["rank"] + "\n" + get_suit_symbol(card["suit"])
		card_content.size = CARD_SIZE
		card_content.add_theme_font_size_override("font_size", 14)
		card_content.add_theme_color_override("font_color", Color.RED if card["color"] == "red" else Color.BLACK)
		card_content.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		card_content.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		card_front.add_child(card_content)

		card_element.add_child(card_front)

	return card_element

func get_suit_symbol(suit: String) -> String:
	var symbols = {
		"Hearts": "♥",
		"Diamonds": "♦",
		"Clubs": "♣",
		"Spades": "♠"
	}
	return symbols.get(suit, "?")

func update_ui():
	# 更新玩家分数
	if player_hand.size() > 0:
		player_score = calculate_hand_value(player_hand)
		ui_elements["player_score"].text = str(player_score)

	# 更新庄家分数
	if dealer_hand.size() > 0:
		if game_state == "playing":
			ui_elements["dealer_score"].text = "?"
		else:
			dealer_score = calculate_hand_value(dealer_hand)
			ui_elements["dealer_score"].text = str(dealer_score)

	# 更新余额和投注
	ui_elements["player_balance"].text = str(player_balance)
	ui_elements["bet_amount"].text = str(current_bet)
	ui_elements["deck_count"].text = str(deck.size())

	# 更新按钮状态
	update_button_states()

func update_button_states():
	var can_play = (game_state == "playing")
	var can_bet = (game_state == "waiting")
	var can_deal = (current_bet > 0 and game_state == "waiting")

	ui_elements["hit_button"].disabled = not can_play
	ui_elements["stand_button"].disabled = not can_play
	ui_elements["double_button"].disabled = not (can_play and player_hand.size() == 2)
	ui_elements["surrender_button"].disabled = not (can_play and player_hand.size() == 2)
	ui_elements["deal_button"].disabled = not can_deal
	ui_elements["clear_button"].disabled = not can_bet

# 按钮事件处理函数
func _on_chip_pressed(value: int):
	if game_state == "waiting" and player_balance >= value:
		current_bet += value
		player_balance -= value
		update_ui()

func _on_clear_bet_pressed():
	if game_state == "waiting":
		player_balance += current_bet
		current_bet = 0
		update_ui()

func _on_deal_pressed():
	if current_bet > 0 and game_state == "waiting":
		start_new_game()

func start_new_game():
	game_state = "playing"
	player_hand.clear()
	dealer_hand.clear()

	# 清除现有卡牌显示
	clear_card_displays()

	# 发牌
	player_hand.append(deal_card())
	dealer_hand.append(deal_card())
	player_hand.append(deal_card())
	dealer_hand.append(deal_card())

	# 显示卡牌
	display_cards()

	update_ui()
	check_blackjack()

func clear_card_displays():
	# 清除玩家卡牌显示
	for child in ui_elements["player_cards"].get_children():
		child.queue_free()

	# 清除庄家卡牌显示
	for child in ui_elements["dealer_cards"].get_children():
		child.queue_free()

func display_cards():
	# 显示玩家卡牌
	for i in range(player_hand.size()):
		var card_element = create_card_element(player_hand[i])
		card_element.position = Vector2(i * (CARD_SIZE.x + 10), 0)
		ui_elements["player_cards"].add_child(card_element)

	# 显示庄家卡牌（第一张隐藏）
	for i in range(dealer_hand.size()):
		var is_hidden = (i == 0 and game_state == "playing")
		var card_element = create_card_element(dealer_hand[i], is_hidden)
		card_element.position = Vector2(i * (CARD_SIZE.x + 10), 0)
		ui_elements["dealer_cards"].add_child(card_element)

func check_blackjack():
	player_score = calculate_hand_value(player_hand)
	dealer_score = calculate_hand_value(dealer_hand)

	if player_score == 21 and player_hand.size() == 2:
		if dealer_score == 21 and dealer_hand.size() == 2:
			end_game("Push! Both have Blackjack!")
		else:
			end_game("Blackjack! You win!")
	elif dealer_score == 21 and dealer_hand.size() == 2:
		end_game("Dealer has Blackjack!")

func _on_hit_pressed():
	if game_state == "playing":
		player_hand.append(deal_card())
		display_cards()
		player_score = calculate_hand_value(player_hand)
		update_ui()

		if player_score > 21:
			end_game("Bust! You lose!")
		elif player_score == 21:
			dealer_turn()

func _on_stand_pressed():
	if game_state == "playing":
		dealer_turn()

func _on_double_pressed():
	if game_state == "playing" and player_hand.size() == 2 and player_balance >= current_bet:
		player_balance -= current_bet
		current_bet *= 2
		player_hand.append(deal_card())
		display_cards()
		player_score = calculate_hand_value(player_hand)
		update_ui()

		if player_score > 21:
			end_game("Bust! You lose!")
		else:
			dealer_turn()

func _on_surrender_pressed():
	if game_state == "playing" and player_hand.size() == 2:
		player_balance += current_bet / 2
		end_game("Surrendered!")

func dealer_turn():
	game_state = "dealer_turn"

	# 显示庄家的隐藏卡牌
	display_cards()

	# 庄家要牌逻辑
	while calculate_hand_value(dealer_hand) < 17:
		dealer_hand.append(deal_card())
		display_cards()

	# 判断胜负
	determine_winner()

func determine_winner():
	player_score = calculate_hand_value(player_hand)
	dealer_score = calculate_hand_value(dealer_hand)

	var winnings = 0
	var message = ""

	if dealer_score > 21:
		winnings = current_bet * 2
		message = "Dealer busts! You win!"
	elif player_score > 21:
		winnings = 0
		message = "Bust! You lose!"
	elif player_score == 21 and player_hand.size() == 2:
		winnings = int(current_bet * 2.5)
		message = "Blackjack! You win!"
	elif dealer_score > player_score:
		winnings = 0
		message = "Dealer wins!"
	elif player_score > dealer_score:
		winnings = current_bet * 2
		message = "You win!"
	else:
		winnings = current_bet
		message = "Push! It's a tie!"

	player_balance += winnings
	end_game(message)

func end_game(message: String):
	game_state = "waiting"
	current_bet = 0
	update_ui()
	print(message)  # 临时用print显示消息，后续可以添加UI消息显示
