extends Node2D

var deck = []
var player_hand = []
var dealer_hand = []
var game_state = "waiting"
var player_score = 0
var dealer_score = 0

var ui_elements = {}

func _ready():
	setup_ui()
	create_deck()
	shuffle_deck()
	start_new_game()

func setup_ui():
	var screen_size = get_viewport().get_visible_rect().size

	var title = Label.new()
	title.text = "BLACKJACK"
	title.position = Vector2(screen_size.x / 2 - 100, 50)
	title.add_theme_font_size_override("font_size", 32)
	add_child(title)

	var player_label = Label.new()
	player_label.text = "Player Cards:"
	player_label.position = Vector2(50, 150)
	player_label.add_theme_font_size_override("font_size", 18)
	add_child(player_label)
	ui_elements["player_label"] = player_label

	var player_cards = Label.new()
	player_cards.text = ""
	player_cards.position = Vector2(50, 180)
	player_cards.add_theme_font_size_override("font_size", 16)
	add_child(player_cards)
	ui_elements["player_cards"] = player_cards

	var player_score_label = Label.new()
	player_score_label.text = "Score: 0"
	player_score_label.position = Vector2(50, 220)
	player_score_label.add_theme_font_size_override("font_size", 16)
	add_child(player_score_label)
	ui_elements["player_score"] = player_score_label

	var dealer_label = Label.new()
	dealer_label.text = "Dealer Cards:"
	dealer_label.position = Vector2(50, 300)
	dealer_label.add_theme_font_size_override("font_size", 18)
	add_child(dealer_label)
	ui_elements["dealer_label"] = dealer_label

	var dealer_cards = Label.new()
	dealer_cards.text = ""
	dealer_cards.position = Vector2(50, 330)
	dealer_cards.add_theme_font_size_override("font_size", 16)
	add_child(dealer_cards)
	ui_elements["dealer_cards"] = dealer_cards

	var dealer_score_label = Label.new()
	dealer_score_label.text = "Score: 0"
	dealer_score_label.position = Vector2(50, 370)
	dealer_score_label.add_theme_font_size_override("font_size", 16)
	add_child(dealer_score_label)
	ui_elements["dealer_score"] = dealer_score_label

	var hit_button = Button.new()
	hit_button.text = "Hit"
	hit_button.position = Vector2(50, 450)
	hit_button.size = Vector2(100, 40)
	hit_button.pressed.connect(_on_hit_pressed)
	add_child(hit_button)
	ui_elements["hit_button"] = hit_button

	var stand_button = Button.new()
	stand_button.text = "Stand"
	stand_button.position = Vector2(170, 450)
	stand_button.size = Vector2(100, 40)
	stand_button.pressed.connect(_on_stand_pressed)
	add_child(stand_button)
	ui_elements["stand_button"] = stand_button

	var new_game_button = Button.new()
	new_game_button.text = "New Game"
	new_game_button.position = Vector2(290, 450)
	new_game_button.size = Vector2(100, 40)
	new_game_button.pressed.connect(_on_new_game_pressed)
	add_child(new_game_button)
	ui_elements["new_game_button"] = new_game_button

	var message_label = Label.new()
	message_label.text = "Welcome to Blackjack!"
	message_label.position = Vector2(50, 520)
	message_label.add_theme_font_size_override("font_size", 18)
	add_child(message_label)
	ui_elements["message"] = message_label

func create_deck():
	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]

	deck.clear()

	for suit in suits:
		for rank in ranks:
			var card = {
				"suit": suit,
				"rank": rank
			}
			deck.append(card)

func shuffle_deck():
	deck.shuffle()

func start_new_game():
	player_hand.clear()
	dealer_hand.clear()
	game_state = "playing"

	if deck.size() < 10:
		create_deck()
		shuffle_deck()

	deal_card(player_hand)
	deal_card(dealer_hand)
	deal_card(player_hand)
	deal_card(dealer_hand)

	update_ui()
	check_blackjack()

func deal_card(hand):
	if deck.size() > 0:
		var card = deck.pop_back()
		hand.append(card)
		return card
	return null

func get_card_value(card):
	var rank = card["rank"]
	if rank in ["J", "Q", "K"]:
		return 10
	elif rank == "A":
		return 11
	else:
		return int(rank)

func calculate_hand_value(hand):
	var total = 0
	var aces = 0

	for card in hand:
		var value = get_card_value(card)
		if card["rank"] == "A":
			aces += 1
		total += value

	while total > 21 and aces > 0:
		total -= 10
		aces -= 1

	return total

func format_hand(hand, hide_first = false):
	var result = ""
	for i in range(hand.size()):
		if i == 0 and hide_first:
			result += "[Hidden] "
		else:
			result += hand[i]["rank"] + " of " + hand[i]["suit"] + " "
	return result

func update_ui():
	player_score = calculate_hand_value(player_hand)
	dealer_score = calculate_hand_value(dealer_hand)

	ui_elements["player_cards"].text = format_hand(player_hand)
	ui_elements["player_score"].text = "Score: " + str(player_score)

	if game_state == "playing":
		ui_elements["dealer_cards"].text = format_hand(dealer_hand, true)
		ui_elements["dealer_score"].text = "Score: ?"
	else:
		ui_elements["dealer_cards"].text = format_hand(dealer_hand)
		ui_elements["dealer_score"].text = "Score: " + str(dealer_score)

	ui_elements["hit_button"].disabled = (game_state != "playing")
	ui_elements["stand_button"].disabled = (game_state != "playing")

func check_blackjack():
	if player_score == 21 and player_hand.size() == 2:
		if dealer_score == 21 and dealer_hand.size() == 2:
			end_game("Push! Both have Blackjack!")
		else:
			end_game("Blackjack! You win!")
	elif dealer_score == 21 and dealer_hand.size() == 2:
		end_game("Dealer has Blackjack! You lose!")

func dealer_turn():
	game_state = "dealer_turn"
	update_ui()

	while calculate_hand_value(dealer_hand) < 17:
		await get_tree().create_timer(1.0).timeout
		deal_card(dealer_hand)
		update_ui()

	var final_dealer_score = calculate_hand_value(dealer_hand)
	var final_player_score = calculate_hand_value(player_hand)

	if final_dealer_score > 21:
		end_game("Dealer busts! You win!")
	elif final_dealer_score > final_player_score:
		end_game("Dealer wins!")
	elif final_player_score > final_dealer_score:
		end_game("You win!")
	else:
		end_game("Push! It's a tie!")

func end_game(message):
	game_state = "game_over"
	ui_elements["message"].text = message
	update_ui()

func _on_hit_pressed():
	if game_state == "playing":
		deal_card(player_hand)
		player_score = calculate_hand_value(player_hand)
		update_ui()

		if player_score > 21:
			end_game("Bust! You lose!")
		elif player_score == 21:
			dealer_turn()

func _on_stand_pressed():
	if game_state == "playing":
		dealer_turn()

func _on_new_game_pressed():
	start_new_game()
	ui_elements["message"].text = "New game started!"
