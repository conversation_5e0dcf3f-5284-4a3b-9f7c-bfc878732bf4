extends Node2D

# 简化版本用于测试基本功能
var deck = []
var player_hand = []
var dealer_hand = []
var game_state = "waiting"
var player_balance = 1000
var current_bet = 0

var ui_elements = {}

func _ready():
	print("Starting test game...")
	
	# 设置窗口大小
	get_viewport().size = Vector2(1200, 800)
	
	# 创建简单的测试UI
	create_test_ui()
	
	# 初始化游戏
	create_deck()
	shuffle_deck()
	
	print("Test game ready!")

func create_test_ui():
	var screen_size = get_viewport().get_visible_rect().size
	
	# 背景
	var background = ColorRect.new()
	background.size = screen_size
	background.color = Color(0.0, 0.4, 0.0)
	add_child(background)
	
	# 标题
	var title = Label.new()
	title.text = "BLACKJACK TEST"
	title.position = Vector2(screen_size.x / 2 - 150, 50)
	title.add_theme_font_size_override("font_size", 36)
	title.add_theme_color_override("font_color", Color.GOLD)
	add_child(title)
	
	# 余额
	var balance_label = Label.new()
	balance_label.text = "Balance: $" + str(player_balance)
	balance_label.position = Vector2(50, 100)
	balance_label.add_theme_font_size_override("font_size", 20)
	balance_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(balance_label)
	ui_elements["balance"] = balance_label
	
	# 投注
	var bet_label = Label.new()
	bet_label.text = "Bet: $" + str(current_bet)
	bet_label.position = Vector2(50, 130)
	bet_label.add_theme_font_size_override("font_size", 20)
	bet_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(bet_label)
	ui_elements["bet"] = bet_label
	
	# 测试筹码按钮
	var chip_btn = Button.new()
	chip_btn.text = "Bet $10"
	chip_btn.position = Vector2(50, 200)
	chip_btn.size = Vector2(100, 50)
	chip_btn.pressed.connect(_on_bet_pressed)
	add_child(chip_btn)
	
	# 发牌按钮
	var deal_btn = Button.new()
	deal_btn.text = "Deal"
	deal_btn.position = Vector2(170, 200)
	deal_btn.size = Vector2(100, 50)
	deal_btn.pressed.connect(_on_deal_pressed)
	add_child(deal_btn)
	ui_elements["deal_button"] = deal_btn
	
	# Hit按钮
	var hit_btn = Button.new()
	hit_btn.text = "Hit"
	hit_btn.position = Vector2(290, 200)
	hit_btn.size = Vector2(100, 50)
	hit_btn.pressed.connect(_on_hit_pressed)
	hit_btn.disabled = true
	add_child(hit_btn)
	ui_elements["hit_button"] = hit_btn
	
	# Stand按钮
	var stand_btn = Button.new()
	stand_btn.text = "Stand"
	stand_btn.position = Vector2(410, 200)
	stand_btn.size = Vector2(100, 50)
	stand_btn.pressed.connect(_on_stand_pressed)
	stand_btn.disabled = true
	add_child(stand_btn)
	ui_elements["stand_button"] = stand_btn
	
	# 玩家卡牌区域
	var player_cards_label = Label.new()
	player_cards_label.text = "Player Cards:"
	player_cards_label.position = Vector2(50, 300)
	player_cards_label.add_theme_font_size_override("font_size", 18)
	player_cards_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(player_cards_label)
	
	var player_cards_area = Control.new()
	player_cards_area.position = Vector2(50, 330)
	player_cards_area.size = Vector2(600, 100)
	add_child(player_cards_area)
	ui_elements["player_cards"] = player_cards_area
	
	# 庄家卡牌区域
	var dealer_cards_label = Label.new()
	dealer_cards_label.text = "Dealer Cards:"
	dealer_cards_label.position = Vector2(50, 450)
	dealer_cards_label.add_theme_font_size_override("font_size", 18)
	dealer_cards_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(dealer_cards_label)
	
	var dealer_cards_area = Control.new()
	dealer_cards_area.position = Vector2(50, 480)
	dealer_cards_area.size = Vector2(600, 100)
	add_child(dealer_cards_area)
	ui_elements["dealer_cards"] = dealer_cards_area
	
	# 消息区域
	var message_label = Label.new()
	message_label.text = "Welcome! Bet $10 and click Deal to start!"
	message_label.position = Vector2(50, 600)
	message_label.add_theme_font_size_override("font_size", 18)
	message_label.add_theme_color_override("font_color", Color.GOLD)
	add_child(message_label)
	ui_elements["message"] = message_label

func create_deck():
	var suits = ["Hearts", "Diamonds", "Clubs", "Spades"]
	var ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
	
	deck.clear()
	
	for suit in suits:
		for rank in ranks:
			var card = {
				"suit": suit,
				"rank": rank,
				"color": "red" if suit in ["Hearts", "Diamonds"] else "black"
			}
			deck.append(card)

func shuffle_deck():
	deck.shuffle()

func _on_bet_pressed():
	if current_bet == 0 and player_balance >= 10:
		current_bet = 10
		player_balance -= 10
		ui_elements["balance"].text = "Balance: $" + str(player_balance)
		ui_elements["bet"].text = "Bet: $" + str(current_bet)
		ui_elements["message"].text = "Bet placed! Click Deal to start!"

func _on_deal_pressed():
	if current_bet > 0:
		ui_elements["message"].text = "Game started! Cards dealt!"
		ui_elements["hit_button"].disabled = false
		ui_elements["stand_button"].disabled = false
		ui_elements["deal_button"].disabled = true

func _on_hit_pressed():
	ui_elements["message"].text = "You hit!"

func _on_stand_pressed():
	ui_elements["message"].text = "You stand!"
	current_bet = 0
	ui_elements["bet"].text = "Bet: $" + str(current_bet)
	ui_elements["hit_button"].disabled = true
	ui_elements["stand_button"].disabled = true
	ui_elements["deal_button"].disabled = false
